import { queryShOptionAppointmentInfo, bussinesInvestorRightQry } from '@/service/service';

/**
 * 业务预处理器 - 负责在流程初始化前进行业务相关的预处理
 * 采用策略模式，支持不同业务类型的扩展
 */
class BusinessPreProcessor {
  constructor() {
    this.strategies = new Map();
  }

  /**
   * 注册业务策略
   * @param {string} bizType - 业务类型
   * @param {Object} strategy - 策略实例
   */
  registerStrategy(bizType, strategy) {
    this.strategies.set(bizType, strategy);
  }

  /**
   * 处理业务预处理逻辑
   * @param {string} bizType - 业务类型
   * @param {Object} context - 上下文对象
   * @returns {Promise<{shouldContinue: boolean}>}
   */
  async process(bizType, context) {
    const strategy = this.strategies.get(bizType);
    if (strategy) {
      return await strategy.execute(context);
    }
    // 默认策略：继续执行流程
    return { shouldContinue: true };
  }
}

/**
 * 期权账户策略 - 处理期权账户相关的预处理逻辑
 */
class OptionAccountStrategy {
  /**
   * 执行期权账户预处理逻辑
   * @param {Object} context - 上下文对象，包含Vue实例等
   * @returns {Promise<{shouldContinue: boolean}>}
   */
  async execute(context) {
    try {
      const response = await queryShOptionAppointmentInfo();

      if (response?.data?.flowInsId) {
        // 需要跳转到期权账户结果页
        return this._handleJumpToOptionResult(context);
      }

      // 继续正常流程
      return { shouldContinue: true };
    } catch (error) {
      console.error('查询期权预约信息失败:', error);
      // 出错时继续正常流程，避免阻塞用户操作
      return { shouldContinue: true };
    }
  }

  /**
   * 处理跳转到期权账户结果页的逻辑
   * @param {Object} context - 上下文对象
   * @returns {{shouldContinue: boolean}}
   * @private
   */
  _handleJumpToOptionResult(context) {
    try {
      // 跳转到期权账户结果页
      context.$router.replace({
        name: 'optionAccountResult'
      });
      return { shouldContinue: false };
    } catch (error) {
      console.error('跳转到期权账户结果页失败:', error);
      return { shouldContinue: true };
    }
  }
}

/**
 * 合格投资者策略 - 处理业务相关的预处理逻辑
 */
class QualifiedInvestorStrategy {
  constructor(bizType) {
    this.bizType = bizType;
  }

  async execute(context) {
    try {
      const response = await bussinesInvestorRightQry({
        opEntrustWay: $hvue.customConfig.opEntrustWay
      });

      const data = response?.data;
      if (!data) {
        // 没有数据时继续正常流程
        return { shouldContinue: true };
      }

      // 根据业务类型检查对应的认证状态
      let isAuthenticated = false;
      if (this.bizType === '010039') {
        // 私募合格投资者业务
        isAuthenticated = data.privateEquityInvestorStatus === '2';
      } else if (this.bizType === '010040') {
        // 资管合格投资者业务
        isAuthenticated = data.assetManageInvestorStatus === '2';
      }

      if (isAuthenticated) {
        // 已认证，显示提示弹窗并阻止流程继续
        this._showAuthCompletedAlert(context);
        return { shouldContinue: false };
      }

      // 未认证，继续正常流程
      return { shouldContinue: true };
    } catch (error) {
      console.error('查询合格投资者状态失败:', error);
      // 出错时继续正常流程，避免阻塞用户操作
      return { shouldContinue: true };
    }
  }

  /**
   * 显示已完成认证的提示弹窗
   * @param {Object} context - 上下文对象，包含Vue实例等
   * @private
   */
  _showAuthCompletedAlert(context) {
    try {
      if (context.$TAlert) {
        context.$TAlert({
          title: '提示',
          tips: '您已完成认证，无需重复办理',
          confirmBtn: '我知道了',
          hasCancel: false,
          confirm: () => {
            // 点击"我知道了"返回上一个页面
            if (context.$router) {
              context.$router.back();
            }
          }
        });
      }
    } catch (error) {
      console.error('显示认证完成提示失败:', error);
    }
  }
}

// 创建全局实例并注册策略
const processor = new BusinessPreProcessor();
processor.registerStrategy('010169', new OptionAccountStrategy());
processor.registerStrategy('010039', new QualifiedInvestorStrategy('010039'));
processor.registerStrategy('010040', new QualifiedInvestorStrategy('010040'));

export default processor;
